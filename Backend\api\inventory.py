"""
API المخزون والأصناف
Inventory and Items API
"""

from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from typing import List, Optional
from datetime import date
from decimal import Decimal

from database.config import get_db
from core.schemas import (
    ItemCategoryResponse, ItemCategoryCreate, ItemCategoryUpdate,
    UnitResponse, UnitCreate, UnitUpdate,
    ItemResponse, ItemCreate, ItemUpdate,
    WarehouseResponse, WarehouseCreate, WarehouseUpdate,
    ItemStockResponse, ItemStockCreate, ItemStockUpdate,
    StockMovementResponse, StockMovementCreate,
    MessageResponse
)
from core.security import require_accounting_read, require_accounting_write
from services.inventory_service import InventoryService
from models.users.user import User

router = APIRouter()

# ===== تصنيفات الأصناف =====

@router.get("/categories", response_model=List[ItemCategoryResponse])
async def get_item_categories(
    parent_id: Optional[int] = Query(None, description="تصفية حسب التصنيف الأب"),
    db: Session = Depends(get_db),
    current_user: User = Depends(require_accounting_read())
):
    """
    الحصول على تصنيفات الأصناف
    Get item categories
    """
    inventory_service = InventoryService(db)
    return inventory_service.get_item_categories(parent_id)

@router.get("/categories/{category_id}", response_model=ItemCategoryResponse)
async def get_item_category(
    category_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(require_accounting_read())
):
    """
    الحصول على تصنيف بالمعرف
    Get item category by ID
    """
    inventory_service = InventoryService(db)
    category = inventory_service.get_item_category_by_id(category_id)
    if not category:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="التصنيف غير موجود"
        )
    return category

@router.post("/categories", response_model=ItemCategoryResponse)
async def create_item_category(
    category_data: ItemCategoryCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(require_accounting_write())
):
    """
    إنشاء تصنيف جديد
    Create new item category
    """
    inventory_service = InventoryService(db)
    try:
        return inventory_service.create_item_category(category_data)
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )

@router.put("/categories/{category_id}", response_model=ItemCategoryResponse)
async def update_item_category(
    category_id: int,
    category_data: ItemCategoryUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(require_accounting_write())
):
    """
    تحديث تصنيف
    Update item category
    """
    inventory_service = InventoryService(db)
    try:
        category = inventory_service.update_item_category(category_id, category_data)
        if not category:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="التصنيف غير موجود"
            )
        return category
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )

# ===== وحدات القياس =====

@router.get("/units", response_model=List[UnitResponse])
async def get_units(
    db: Session = Depends(get_db),
    current_user: User = Depends(require_accounting_read())
):
    """
    الحصول على وحدات القياس
    Get units of measure
    """
    inventory_service = InventoryService(db)
    return inventory_service.get_units()

@router.get("/units/{unit_id}", response_model=UnitResponse)
async def get_unit(
    unit_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(require_accounting_read())
):
    """
    الحصول على وحدة بالمعرف
    Get unit by ID
    """
    inventory_service = InventoryService(db)
    unit = inventory_service.get_unit_by_id(unit_id)
    if not unit:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="وحدة القياس غير موجودة"
        )
    return unit

@router.post("/units", response_model=UnitResponse)
async def create_unit(
    unit_data: UnitCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(require_accounting_write())
):
    """
    إنشاء وحدة قياس جديدة
    Create new unit of measure
    """
    inventory_service = InventoryService(db)
    try:
        return inventory_service.create_unit(unit_data)
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )

@router.put("/units/{unit_id}", response_model=UnitResponse)
async def update_unit(
    unit_id: int,
    unit_data: UnitUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(require_accounting_write())
):
    """
    تحديث وحدة قياس
    Update unit of measure
    """
    inventory_service = InventoryService(db)
    try:
        unit = inventory_service.update_unit(unit_id, unit_data)
        if not unit:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="وحدة القياس غير موجودة"
            )
        return unit
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )

# ===== الأصناف =====

@router.get("/items", response_model=List[ItemResponse])
async def get_items(
    category_id: Optional[int] = Query(None, description="تصفية حسب التصنيف"),
    item_type: Optional[str] = Query(None, description="تصفية حسب نوع الصنف"),
    is_active: Optional[bool] = Query(None, description="تصفية حسب الحالة"),
    search: Optional[str] = Query(None, description="البحث في رقم الصنف أو الاسم أو الباركود"),
    low_stock_only: bool = Query(False, description="الأصناف التي وصلت للحد الأدنى فقط"),
    db: Session = Depends(get_db),
    current_user: User = Depends(require_accounting_read())
):
    """
    الحصول على الأصناف مع إمكانية التصفية
    Get items with filtering options
    """
    inventory_service = InventoryService(db)
    return inventory_service.get_items(
        category_id=category_id,
        item_type=item_type,
        is_active=is_active,
        search=search,
        low_stock_only=low_stock_only
    )

@router.get("/items/{item_id}", response_model=ItemResponse)
async def get_item(
    item_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(require_accounting_read())
):
    """
    الحصول على صنف بالمعرف
    Get item by ID
    """
    inventory_service = InventoryService(db)
    item = inventory_service.get_item_by_id(item_id)
    if not item:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="الصنف غير موجود"
        )
    return item

@router.get("/items/by-number/{item_number}", response_model=ItemResponse)
async def get_item_by_number(
    item_number: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(require_accounting_read())
):
    """
    الحصول على صنف برقم الصنف
    Get item by item number
    """
    inventory_service = InventoryService(db)
    item = inventory_service.get_item_by_number(item_number)
    if not item:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="الصنف غير موجود"
        )
    return item

@router.get("/items/by-barcode/{barcode}", response_model=ItemResponse)
async def get_item_by_barcode(
    barcode: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(require_accounting_read())
):
    """
    الحصول على صنف بالباركود
    Get item by barcode
    """
    inventory_service = InventoryService(db)
    item = inventory_service.get_item_by_barcode(barcode)
    if not item:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="الصنف غير موجود"
        )
    return item

@router.post("/items", response_model=ItemResponse)
async def create_item(
    item_data: ItemCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(require_accounting_write())
):
    """
    إنشاء صنف جديد
    Create new item
    """
    inventory_service = InventoryService(db)
    try:
        return inventory_service.create_item(item_data)
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )

@router.put("/items/{item_id}", response_model=ItemResponse)
async def update_item(
    item_id: int,
    item_data: ItemUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(require_accounting_write())
):
    """
    تحديث صنف
    Update item
    """
    inventory_service = InventoryService(db)
    try:
        item = inventory_service.update_item(item_id, item_data)
        if not item:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="الصنف غير موجود"
            )
        return item
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )

@router.delete("/items/{item_id}", response_model=MessageResponse)
async def delete_item(
    item_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(require_accounting_write())
):
    """
    حذف صنف
    Delete item
    """
    inventory_service = InventoryService(db)
    success = inventory_service.delete_item(item_id)
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="الصنف غير موجود"
        )
    return MessageResponse(
        message="تم حذف الصنف بنجاح",
        message_ar="تم حذف الصنف بنجاح"
    )

# ===== المخازن =====

@router.get("/warehouses", response_model=List[WarehouseResponse])
async def get_warehouses(
    is_active: Optional[bool] = Query(None, description="تصفية حسب الحالة"),
    db: Session = Depends(get_db),
    current_user: User = Depends(require_accounting_read())
):
    """
    الحصول على المخازن
    Get warehouses
    """
    inventory_service = InventoryService(db)
    return inventory_service.get_warehouses(is_active)

@router.get("/warehouses/{warehouse_id}", response_model=WarehouseResponse)
async def get_warehouse(
    warehouse_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(require_accounting_read())
):
    """
    الحصول على مخزن بالمعرف
    Get warehouse by ID
    """
    inventory_service = InventoryService(db)
    warehouse = inventory_service.get_warehouse_by_id(warehouse_id)
    if not warehouse:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="المخزن غير موجود"
        )
    return warehouse

@router.post("/warehouses", response_model=WarehouseResponse)
async def create_warehouse(
    warehouse_data: WarehouseCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(require_accounting_write())
):
    """
    إنشاء مخزن جديد
    Create new warehouse
    """
    inventory_service = InventoryService(db)
    try:
        return inventory_service.create_warehouse(warehouse_data)
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )

@router.put("/warehouses/{warehouse_id}", response_model=WarehouseResponse)
async def update_warehouse(
    warehouse_id: int,
    warehouse_data: WarehouseUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(require_accounting_write())
):
    """
    تحديث مخزن
    Update warehouse
    """
    inventory_service = InventoryService(db)
    warehouse = inventory_service.update_warehouse(warehouse_id, warehouse_data)
    if not warehouse:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="المخزن غير موجود"
        )
    return warehouse

# ===== مخزون الأصناف =====

@router.get("/items/{item_id}/stock", response_model=List[ItemStockResponse])
async def get_item_stocks(
    item_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(require_accounting_read())
):
    """
    الحصول على مخزون صنف في جميع المخازن
    Get item stock in all warehouses
    """
    inventory_service = InventoryService(db)
    return inventory_service.get_item_stocks_by_item(item_id)

@router.get("/warehouses/{warehouse_id}/stock", response_model=List[ItemStockResponse])
async def get_warehouse_stocks(
    warehouse_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(require_accounting_read())
):
    """
    الحصول على مخزون جميع الأصناف في مخزن محدد
    Get all items stock in specific warehouse
    """
    inventory_service = InventoryService(db)
    return inventory_service.get_item_stocks_by_warehouse(warehouse_id)

@router.get("/stock/{item_id}/{warehouse_id}", response_model=ItemStockResponse)
async def get_item_stock(
    item_id: int,
    warehouse_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(require_accounting_read())
):
    """
    الحصول على مخزون صنف في مخزن محدد
    Get item stock in specific warehouse
    """
    inventory_service = InventoryService(db)
    stock = inventory_service.get_item_stock(item_id, warehouse_id)
    if not stock:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="مخزون الصنف غير موجود في هذا المخزن"
        )
    return stock

@router.post("/stock/adjust", response_model=ItemStockResponse)
async def adjust_item_stock(
    item_id: int,
    warehouse_id: int,
    quantity_change: Decimal,
    unit_cost: Decimal = Decimal('0.00'),
    description: Optional[str] = None,
    db: Session = Depends(get_db),
    current_user: User = Depends(require_accounting_write())
):
    """
    تعديل مخزون صنف
    Adjust item stock
    """
    inventory_service = InventoryService(db)
    try:
        return inventory_service.adjust_item_stock(
            item_id=item_id,
            warehouse_id=warehouse_id,
            quantity_change=quantity_change,
            unit_cost=unit_cost,
            user_id=current_user.id,
            description=description
        )
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )

# ===== حركات المخزون =====

@router.get("/movements", response_model=List[StockMovementResponse])
async def get_stock_movements(
    item_id: Optional[int] = Query(None, description="تصفية حسب الصنف"),
    warehouse_id: Optional[int] = Query(None, description="تصفية حسب المخزن"),
    movement_type: Optional[str] = Query(None, description="تصفية حسب نوع الحركة"),
    start_date: Optional[date] = Query(None, description="من تاريخ"),
    end_date: Optional[date] = Query(None, description="إلى تاريخ"),
    db: Session = Depends(get_db),
    current_user: User = Depends(require_accounting_read())
):
    """
    الحصول على حركات المخزون مع إمكانية التصفية
    Get stock movements with filtering options
    """
    inventory_service = InventoryService(db)
    return inventory_service.get_stock_movements(
        item_id=item_id,
        warehouse_id=warehouse_id,
        movement_type=movement_type,
        start_date=start_date,
        end_date=end_date
    )

# ===== التقارير =====

@router.get("/reports/low-stock", response_model=List[ItemResponse])
async def get_low_stock_report(
    db: Session = Depends(get_db),
    current_user: User = Depends(require_accounting_read())
):
    """
    تقرير الأصناف التي وصلت للحد الأدنى
    Low stock items report
    """
    inventory_service = InventoryService(db)
    return inventory_service.get_low_stock_items()

@router.get("/reports/stock-valuation")
async def get_stock_valuation_report(
    warehouse_id: Optional[int] = Query(None, description="تقييم مخزن محدد"),
    db: Session = Depends(get_db),
    current_user: User = Depends(require_accounting_read())
):
    """
    تقرير تقييم المخزون
    Stock valuation report
    """
    inventory_service = InventoryService(db)
    return inventory_service.get_stock_valuation(warehouse_id)
