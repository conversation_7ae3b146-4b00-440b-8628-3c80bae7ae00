"""
نماذج الأصناف والوحدات
Items and Units Models
"""

from sqlalchemy import Column, Integer, String, Boolean, DateTime, Text, Numeric, ForeignKey
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from database.config import Base

class ItemCategory(Base):
    """
    نموذج تصنيفات الأصناف
    Item Categories Model
    """
    __tablename__ = "item_categories"

    # المعرف الفريد
    id = Column(Integer, primary_key=True, index=True)

    # اسم التصنيف
    name = Column(String(100), nullable=False)

    # اسم التصنيف بالعربية
    name_ar = Column(String(150), nullable=False)

    # رمز التصنيف
    code = Column(String(20), unique=True, nullable=False)

    # التصنيف الأب
    parent_id = Column(Integer, ForeignKey('item_categories.id'), nullable=True)

    # وصف التصنيف
    description = Column(Text, nullable=True)

    # هل التصنيف نشط
    is_active = Column(Boolean, default=True)

    # تاريخ الإنشاء
    created_at = Column(DateTime(timezone=True), server_default=func.now())

    # العلاقات
    parent = relationship("ItemCategory", remote_side=[id], backref="children")
    items = relationship("Item", back_populates="category")

    def __repr__(self):
        return f"<ItemCategory(id={self.id}, name='{self.name}', code='{self.code}')>"

class Unit(Base):
    """
    نموذج وحدات القياس
    Units of Measure Model
    """
    __tablename__ = "units"

    # المعرف الفريد
    id = Column(Integer, primary_key=True, index=True)

    # اسم الوحدة
    name = Column(String(50), unique=True, nullable=False)

    # اسم الوحدة بالعربية
    name_ar = Column(String(100), nullable=False)

    # رمز الوحدة
    symbol = Column(String(10), nullable=False)

    # رمز الوحدة بالعربية
    symbol_ar = Column(String(20), nullable=False)

    # هل الوحدة أساسية
    is_base_unit = Column(Boolean, default=False)

    # معامل التحويل للوحدة الأساسية
    conversion_factor = Column(Numeric(10, 4), default=1.0000)

    # هل الوحدة نشطة
    is_active = Column(Boolean, default=True)

    # تاريخ الإنشاء
    created_at = Column(DateTime(timezone=True), server_default=func.now())

    # العلاقات
    items = relationship("Item", back_populates="unit")

    def __repr__(self):
        return f"<Unit(id={self.id}, name='{self.name}', symbol='{self.symbol}')>"

class Item(Base):
    """
    نموذج الصنف
    Item Model
    """
    __tablename__ = "items"

    # المعرف الفريد
    id = Column(Integer, primary_key=True, index=True)

    # رقم الصنف
    item_number = Column(String(50), unique=True, nullable=False, index=True)

    # اسم الصنف
    name = Column(String(200), nullable=False)

    # اسم الصنف بالعربية
    name_ar = Column(String(300), nullable=False)

    # الباركود
    barcode = Column(String(50), unique=True, nullable=True, index=True)

    # تصنيف الصنف
    category_id = Column(Integer, ForeignKey('item_categories.id'), nullable=True)

    # وحدة القياس
    unit_id = Column(Integer, ForeignKey('units.id'), nullable=False)

    # نوع الصنف (product, service, raw_material)
    item_type = Column(String(20), default='product')

    # سعر الشراء
    purchase_price = Column(Numeric(15, 2), default=0.00)

    # سعر البيع
    selling_price = Column(Numeric(15, 2), default=0.00)

    # الحد الأدنى للمخزون
    minimum_stock = Column(Numeric(10, 2), default=0.00)

    # الحد الأقصى للمخزون
    maximum_stock = Column(Numeric(10, 2), default=0.00)

    # نقطة إعادة الطلب
    reorder_point = Column(Numeric(10, 2), default=0.00)

    # وصف الصنف
    description = Column(Text, nullable=True)

    # ملاحظات
    notes = Column(Text, nullable=True)

    # هل الصنف نشط
    is_active = Column(Boolean, default=True)

    # هل يتم تتبع المخزون
    track_inventory = Column(Boolean, default=True)

    # تاريخ الإنشاء
    created_at = Column(DateTime(timezone=True), server_default=func.now())

    # تاريخ آخر تحديث
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # العلاقات
    category = relationship("ItemCategory", back_populates="items")
    unit = relationship("Unit", back_populates="items")
    stock_movements = relationship("StockMovement", back_populates="item")
    item_stocks = relationship("ItemStock", back_populates="item")

    def __repr__(self):
        return f"<Item(id={self.id}, number='{self.item_number}', name='{self.name}')>"

    @property
    def display_name(self):
        """الاسم المعروض"""
        return f"{self.item_number} - {self.name}"

    @property
    def display_name_ar(self):
        """الاسم المعروض بالعربية"""
        return f"{self.item_number} - {self.name_ar}"

    @property
    def total_stock(self):
        """إجمالي المخزون في جميع المخازن"""
        if not self.item_stocks:
            return 0
        return sum(stock.available_quantity for stock in self.item_stocks)

    @property
    def total_reserved(self):
        """إجمالي الكمية المحجوزة في جميع المخازن"""
        if not self.item_stocks:
            return 0
        return sum(stock.reserved_quantity for stock in self.item_stocks)

    @property
    def is_low_stock(self):
        """هل المخزون أقل من الحد الأدنى"""
        return self.total_stock <= self.minimum_stock

    @property
    def profit_margin(self):
        """هامش الربح"""
        if self.purchase_price == 0:
            return 0
        return ((self.selling_price - self.purchase_price) / self.purchase_price) * 100
