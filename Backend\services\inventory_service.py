"""
خدمة إدارة المخزون والأصناف
Inventory and Items Management Service
"""

from sqlalchemy.orm import Session, joinedload
from sqlalchemy import and_, or_, func
from typing import List, Optional, Dict, Any
from datetime import date, datetime
from decimal import Decimal

from models.inventory.item import ItemCategory, Unit, Item
from models.inventory.warehouse import Warehouse, ItemStock, StockMovement
from models.users.user import User
from core.schemas import (
    ItemCategoryCreate, ItemCategoryUpdate,
    UnitCreate, UnitUpdate,
    ItemCreate, ItemUpdate,
    WarehouseCreate, WarehouseUpdate,
    ItemStockCreate, ItemStockUpdate,
    StockMovementCreate
)


class InventoryService:
    """خدمة إدارة المخزون والأصناف"""

    def __init__(self, db: Session):
        self.db = db

    # ===== إدارة تصنيفات الأصناف =====

    def get_item_categories(self, parent_id: Optional[int] = None) -> List[ItemCategory]:
        """الحصول على تصنيفات الأصناف"""
        query = self.db.query(ItemCategory).filter(ItemCategory.is_active == True)

        if parent_id is not None:
            query = query.filter(ItemCategory.parent_id == parent_id)

        return query.order_by(ItemCategory.name).all()

    def get_item_category_by_id(self, category_id: int) -> Optional[ItemCategory]:
        """الحصول على تصنيف بالمعرف"""
        return self.db.query(ItemCategory).filter(ItemCategory.id == category_id).first()

    def create_item_category(self, category_data: ItemCategoryCreate) -> ItemCategory:
        """إنشاء تصنيف جديد"""
        # التحقق من عدم تكرار الرمز
        existing = self.db.query(ItemCategory).filter(ItemCategory.code == category_data.code).first()
        if existing:
            raise ValueError(f"رمز التصنيف '{category_data.code}' موجود مسبقاً")

        category = ItemCategory(**category_data.dict())
        self.db.add(category)
        self.db.commit()
        self.db.refresh(category)
        return category

    def update_item_category(self, category_id: int, category_data: ItemCategoryUpdate) -> Optional[ItemCategory]:
        """تحديث تصنيف"""
        category = self.get_item_category_by_id(category_id)
        if not category:
            return None

        # التحقق من عدم تكرار الرمز
        if category_data.code and category_data.code != category.code:
            existing = self.db.query(ItemCategory).filter(
                and_(ItemCategory.code == category_data.code, ItemCategory.id != category_id)
            ).first()
            if existing:
                raise ValueError(f"رمز التصنيف '{category_data.code}' موجود مسبقاً")

        for field, value in category_data.dict(exclude_unset=True).items():
            setattr(category, field, value)

        self.db.commit()
        self.db.refresh(category)
        return category

    # ===== إدارة وحدات القياس =====

    def get_units(self) -> List[Unit]:
        """الحصول على وحدات القياس"""
        return self.db.query(Unit).filter(Unit.is_active == True).order_by(Unit.name).all()

    def get_unit_by_id(self, unit_id: int) -> Optional[Unit]:
        """الحصول على وحدة بالمعرف"""
        return self.db.query(Unit).filter(Unit.id == unit_id).first()

    def create_unit(self, unit_data: UnitCreate) -> Unit:
        """إنشاء وحدة جديدة"""
        # التحقق من عدم تكرار الاسم
        existing = self.db.query(Unit).filter(Unit.name == unit_data.name).first()
        if existing:
            raise ValueError(f"وحدة القياس '{unit_data.name}' موجودة مسبقاً")

        unit = Unit(**unit_data.dict())
        self.db.add(unit)
        self.db.commit()
        self.db.refresh(unit)
        return unit

    def update_unit(self, unit_id: int, unit_data: UnitUpdate) -> Optional[Unit]:
        """تحديث وحدة"""
        unit = self.get_unit_by_id(unit_id)
        if not unit:
            return None

        # التحقق من عدم تكرار الاسم
        if unit_data.name and unit_data.name != unit.name:
            existing = self.db.query(Unit).filter(
                and_(Unit.name == unit_data.name, Unit.id != unit_id)
            ).first()
            if existing:
                raise ValueError(f"وحدة القياس '{unit_data.name}' موجودة مسبقاً")

        for field, value in unit_data.dict(exclude_unset=True).items():
            setattr(unit, field, value)

        self.db.commit()
        self.db.refresh(unit)
        return unit

    # ===== إدارة الأصناف =====

    def get_items(self,
                  category_id: Optional[int] = None,
                  item_type: Optional[str] = None,
                  is_active: Optional[bool] = None,
                  search: Optional[str] = None,
                  low_stock_only: bool = False) -> List[Item]:
        """الحصول على الأصناف مع إمكانية التصفية"""
        query = self.db.query(Item).options(
            joinedload(Item.category),
            joinedload(Item.unit),
            joinedload(Item.item_stocks)
        )

        if is_active is not None:
            query = query.filter(Item.is_active == is_active)

        if category_id:
            query = query.filter(Item.category_id == category_id)

        if item_type:
            query = query.filter(Item.item_type == item_type)

        if search:
            search_term = f"%{search}%"
            query = query.filter(
                or_(
                    Item.item_number.ilike(search_term),
                    Item.name.ilike(search_term),
                    Item.name_ar.ilike(search_term),
                    Item.barcode.ilike(search_term)
                )
            )

        items = query.order_by(Item.item_number).all()

        if low_stock_only:
            items = [item for item in items if item.is_low_stock]

        return items

    def get_item_by_id(self, item_id: int) -> Optional[Item]:
        """الحصول على صنف بالمعرف"""
        return self.db.query(Item).options(
            joinedload(Item.category),
            joinedload(Item.unit),
            joinedload(Item.item_stocks).joinedload(ItemStock.warehouse)
        ).filter(Item.id == item_id).first()

    def get_item_by_number(self, item_number: str) -> Optional[Item]:
        """الحصول على صنف برقم الصنف"""
        return self.db.query(Item).filter(Item.item_number == item_number).first()

    def get_item_by_barcode(self, barcode: str) -> Optional[Item]:
        """الحصول على صنف بالباركود"""
        return self.db.query(Item).filter(Item.barcode == barcode).first()

    def create_item(self, item_data: ItemCreate) -> Item:
        """إنشاء صنف جديد"""
        # التحقق من عدم تكرار رقم الصنف
        existing = self.db.query(Item).filter(Item.item_number == item_data.item_number).first()
        if existing:
            raise ValueError(f"رقم الصنف '{item_data.item_number}' موجود مسبقاً")

        # التحقق من عدم تكرار الباركود
        if item_data.barcode:
            existing_barcode = self.db.query(Item).filter(Item.barcode == item_data.barcode).first()
            if existing_barcode:
                raise ValueError(f"الباركود '{item_data.barcode}' موجود مسبقاً")

        # التحقق من وجود التصنيف والوحدة
        if item_data.category_id:
            category = self.get_item_category_by_id(item_data.category_id)
            if not category:
                raise ValueError("التصنيف المحدد غير موجود")

        unit = self.get_unit_by_id(item_data.unit_id)
        if not unit:
            raise ValueError("وحدة القياس المحددة غير موجودة")

        item = Item(**item_data.dict())
        self.db.add(item)
        self.db.commit()
        self.db.refresh(item)
        return item

    def update_item(self, item_id: int, item_data: ItemUpdate) -> Optional[Item]:
        """تحديث صنف"""
        item = self.get_item_by_id(item_id)
        if not item:
            return None

        # التحقق من عدم تكرار الباركود
        if item_data.barcode and item_data.barcode != item.barcode:
            existing = self.db.query(Item).filter(
                and_(Item.barcode == item_data.barcode, Item.id != item_id)
            ).first()
            if existing:
                raise ValueError(f"الباركود '{item_data.barcode}' موجود مسبقاً")

        # التحقق من وجود التصنيف والوحدة
        if item_data.category_id and item_data.category_id != item.category_id:
            category = self.get_item_category_by_id(item_data.category_id)
            if not category:
                raise ValueError("التصنيف المحدد غير موجود")

        if item_data.unit_id and item_data.unit_id != item.unit_id:
            unit = self.get_unit_by_id(item_data.unit_id)
            if not unit:
                raise ValueError("وحدة القياس المحددة غير موجودة")

        for field, value in item_data.dict(exclude_unset=True).items():
            setattr(item, field, value)

        self.db.commit()
        self.db.refresh(item)
        return item

    def delete_item(self, item_id: int) -> bool:
        """حذف صنف (إلغاء تفعيل)"""
        item = self.get_item_by_id(item_id)
        if not item:
            return False

        # التحقق من عدم وجود حركات مخزون
        movements_count = self.db.query(StockMovement).filter(StockMovement.item_id == item_id).count()
        if movements_count > 0:
            # إلغاء التفعيل بدلاً من الحذف
            item.is_active = False
            self.db.commit()
            return True

        # حذف فعلي إذا لم توجد حركات
        self.db.delete(item)
        self.db.commit()
        return True

    # ===== إدارة المخازن =====

    def get_warehouses(self, is_active: Optional[bool] = None) -> List[Warehouse]:
        """الحصول على المخازن"""
        query = self.db.query(Warehouse)

        if is_active is not None:
            query = query.filter(Warehouse.is_active == is_active)

        return query.order_by(Warehouse.warehouse_number).all()

    def get_warehouse_by_id(self, warehouse_id: int) -> Optional[Warehouse]:
        """الحصول على مخزن بالمعرف"""
        return self.db.query(Warehouse).filter(Warehouse.id == warehouse_id).first()

    def get_warehouse_by_number(self, warehouse_number: str) -> Optional[Warehouse]:
        """الحصول على مخزن برقم المخزن"""
        return self.db.query(Warehouse).filter(Warehouse.warehouse_number == warehouse_number).first()

    def create_warehouse(self, warehouse_data: WarehouseCreate) -> Warehouse:
        """إنشاء مخزن جديد"""
        # التحقق من عدم تكرار رقم المخزن
        existing = self.db.query(Warehouse).filter(Warehouse.warehouse_number == warehouse_data.warehouse_number).first()
        if existing:
            raise ValueError(f"رقم المخزن '{warehouse_data.warehouse_number}' موجود مسبقاً")

        warehouse = Warehouse(**warehouse_data.dict())
        self.db.add(warehouse)
        self.db.commit()
        self.db.refresh(warehouse)
        return warehouse

    def update_warehouse(self, warehouse_id: int, warehouse_data: WarehouseUpdate) -> Optional[Warehouse]:
        """تحديث مخزن"""
        warehouse = self.get_warehouse_by_id(warehouse_id)
        if not warehouse:
            return None

        for field, value in warehouse_data.dict(exclude_unset=True).items():
            setattr(warehouse, field, value)

        self.db.commit()
        self.db.refresh(warehouse)
        return warehouse

    # ===== إدارة مخزون الأصناف =====

    def get_item_stock(self, item_id: int, warehouse_id: int) -> Optional[ItemStock]:
        """الحصول على مخزون صنف في مخزن محدد"""
        return self.db.query(ItemStock).filter(
            and_(ItemStock.item_id == item_id, ItemStock.warehouse_id == warehouse_id)
        ).first()

    def get_item_stocks_by_item(self, item_id: int) -> List[ItemStock]:
        """الحصول على مخزون صنف في جميع المخازن"""
        return self.db.query(ItemStock).options(
            joinedload(ItemStock.warehouse)
        ).filter(ItemStock.item_id == item_id).all()

    def get_item_stocks_by_warehouse(self, warehouse_id: int) -> List[ItemStock]:
        """الحصول على مخزون جميع الأصناف في مخزن محدد"""
        return self.db.query(ItemStock).options(
            joinedload(ItemStock.item)
        ).filter(ItemStock.warehouse_id == warehouse_id).all()

    def create_or_update_item_stock(self, item_id: int, warehouse_id: int, stock_data: ItemStockCreate) -> ItemStock:
        """إنشاء أو تحديث مخزون صنف"""
        existing_stock = self.get_item_stock(item_id, warehouse_id)

        if existing_stock:
            # تحديث المخزون الموجود
            for field, value in stock_data.dict(exclude_unset=True).items():
                if field not in ['item_id', 'warehouse_id']:  # لا نحدث المعرفات
                    setattr(existing_stock, field, value)

            self.db.commit()
            self.db.refresh(existing_stock)
            return existing_stock
        else:
            # إنشاء مخزون جديد
            stock = ItemStock(**stock_data.dict())
            self.db.add(stock)
            self.db.commit()
            self.db.refresh(stock)
            return stock

    def adjust_item_stock(self, item_id: int, warehouse_id: int, quantity_change: Decimal,
                         unit_cost: Decimal = Decimal('0.00'), user_id: int = None,
                         description: str = None) -> ItemStock:
        """تعديل مخزون صنف"""
        stock = self.get_item_stock(item_id, warehouse_id)

        if not stock:
            # إنشاء مخزون جديد إذا لم يكن موجوداً
            stock_data = ItemStockCreate(
                item_id=item_id,
                warehouse_id=warehouse_id,
                available_quantity=max(quantity_change, Decimal('0')),
                average_cost=unit_cost
            )
            stock = self.create_or_update_item_stock(item_id, warehouse_id, stock_data)
        else:
            # تحديث الكمية المتاحة
            new_quantity = stock.available_quantity + quantity_change
            if new_quantity < 0:
                raise ValueError("لا يمكن أن تكون الكمية المتاحة أقل من الصفر")

            # تحديث متوسط التكلفة (FIFO)
            if quantity_change > 0 and unit_cost > 0:
                total_value = (stock.available_quantity * stock.average_cost) + (quantity_change * unit_cost)
                total_quantity = stock.available_quantity + quantity_change
                stock.average_cost = total_value / total_quantity if total_quantity > 0 else unit_cost

            stock.available_quantity = new_quantity
            self.db.commit()
            self.db.refresh(stock)

        # تسجيل حركة المخزون
        if user_id:
            movement_type = "in" if quantity_change > 0 else "out"
            if quantity_change == 0:
                movement_type = "adjustment"

            self.create_stock_movement(
                item_id=item_id,
                warehouse_id=warehouse_id,
                movement_type=movement_type,
                quantity=abs(quantity_change),
                unit_cost=unit_cost,
                description=description or f"تعديل مخزون - {movement_type}",
                user_id=user_id
            )

        return stock

    # ===== إدارة حركات المخزون =====

    def get_stock_movements(self,
                           item_id: Optional[int] = None,
                           warehouse_id: Optional[int] = None,
                           movement_type: Optional[str] = None,
                           start_date: Optional[date] = None,
                           end_date: Optional[date] = None) -> List[StockMovement]:
        """الحصول على حركات المخزون مع إمكانية التصفية"""
        query = self.db.query(StockMovement).options(
            joinedload(StockMovement.item),
            joinedload(StockMovement.warehouse)
        )

        if item_id:
            query = query.filter(StockMovement.item_id == item_id)

        if warehouse_id:
            query = query.filter(StockMovement.warehouse_id == warehouse_id)

        if movement_type:
            query = query.filter(StockMovement.movement_type == movement_type)

        if start_date:
            query = query.filter(StockMovement.movement_date >= start_date)

        if end_date:
            query = query.filter(StockMovement.movement_date <= end_date)

        return query.order_by(StockMovement.movement_date.desc(), StockMovement.created_at.desc()).all()

    def create_stock_movement(self, item_id: int, warehouse_id: int, movement_type: str,
                             quantity: Decimal, unit_cost: Decimal = Decimal('0.00'),
                             reference: str = None, reference_type: str = None,
                             reference_id: int = None, description: str = None,
                             user_id: int = None, movement_date: date = None) -> StockMovement:
        """إنشاء حركة مخزون"""
        if not movement_date:
            movement_date = date.today()

        # توليد رقم الحركة
        movement_number = self._generate_movement_number(movement_type, movement_date)

        movement_data = {
            'movement_number': movement_number,
            'movement_date': movement_date,
            'item_id': item_id,
            'warehouse_id': warehouse_id,
            'movement_type': movement_type,
            'quantity': quantity,
            'unit_cost': unit_cost,
            'total_cost': quantity * unit_cost,
            'reference': reference,
            'reference_type': reference_type,
            'reference_id': reference_id,
            'description': description,
            'created_by': user_id
        }

        movement = StockMovement(**movement_data)
        self.db.add(movement)
        self.db.commit()
        self.db.refresh(movement)
        return movement

    def _generate_movement_number(self, movement_type: str, movement_date: date) -> str:
        """توليد رقم حركة المخزون"""
        # الحصول على آخر رقم في نفس اليوم
        date_str = movement_date.strftime("%Y%m%d")
        prefix_map = {
            'in': 'IN',
            'out': 'OUT',
            'transfer': 'TR',
            'adjustment': 'ADJ'
        }
        prefix = prefix_map.get(movement_type, 'MOV')

        last_movement = self.db.query(StockMovement).filter(
            and_(
                StockMovement.movement_date == movement_date,
                StockMovement.movement_type == movement_type
            )
        ).order_by(StockMovement.id.desc()).first()

        if last_movement and last_movement.movement_number.startswith(f"{prefix}{date_str}"):
            # استخراج الرقم التسلسلي
            try:
                last_seq = int(last_movement.movement_number[-3:])
                new_seq = last_seq + 1
            except:
                new_seq = 1
        else:
            new_seq = 1

        return f"{prefix}{date_str}{new_seq:03d}"

    # ===== تقارير المخزون =====

    def get_low_stock_items(self) -> List[Item]:
        """الحصول على الأصناف التي وصلت للحد الأدنى"""
        items = self.get_items(is_active=True)
        return [item for item in items if item.is_low_stock]

    def get_stock_valuation(self, warehouse_id: Optional[int] = None) -> Dict[str, Any]:
        """تقييم المخزون"""
        query = self.db.query(ItemStock).options(joinedload(ItemStock.item))

        if warehouse_id:
            query = query.filter(ItemStock.warehouse_id == warehouse_id)

        stocks = query.all()

        total_value = Decimal('0.00')
        total_items = 0
        categories_summary = {}

        for stock in stocks:
            if stock.available_quantity > 0:
                item_value = stock.available_quantity * stock.average_cost
                total_value += item_value
                total_items += 1

                # تجميع حسب التصنيف
                category_name = stock.item.category.name if stock.item.category else "بدون تصنيف"
                if category_name not in categories_summary:
                    categories_summary[category_name] = {
                        'items_count': 0,
                        'total_value': Decimal('0.00'),
                        'total_quantity': Decimal('0.00')
                    }

                categories_summary[category_name]['items_count'] += 1
                categories_summary[category_name]['total_value'] += item_value
                categories_summary[category_name]['total_quantity'] += stock.available_quantity

        return {
            'total_value': total_value,
            'total_items': total_items,
            'categories_summary': categories_summary,
            'valuation_date': datetime.now()
        }
