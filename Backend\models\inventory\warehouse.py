"""
نماذج المخازن وحركات المخزون
Warehouse and Stock Movement Models
"""

from sqlalchemy import Column, Integer, String, Boolean, DateTime, Text, Numeric, ForeignKey, Date, UniqueConstraint
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from database.config import Base

class Warehouse(Base):
    """
    نموذج المخزن
    Warehouse Model
    """
    __tablename__ = "warehouses"

    # المعرف الفريد
    id = Column(Integer, primary_key=True, index=True)

    # رقم المخزن
    warehouse_number = Column(String(20), unique=True, nullable=False, index=True)

    # اسم المخزن
    name = Column(String(100), nullable=False)

    # اسم المخزن بالعربية
    name_ar = Column(String(150), nullable=False)

    # العنوان
    address = Column(Text, nullable=True)

    # المدينة
    city = Column(String(50), nullable=True)

    # المنطقة
    region = Column(String(50), nullable=True)

    # رقم الهاتف
    phone = Column(String(20), nullable=True)

    # اسم المسؤول
    manager_name = Column(String(100), nullable=True)

    # هل المخزن رئيسي
    is_main = Column(Boolean, default=False)

    # هل المخزن نشط
    is_active = Column(Boolean, default=True)

    # ملاحظات
    notes = Column(Text, nullable=True)

    # تاريخ الإنشاء
    created_at = Column(DateTime(timezone=True), server_default=func.now())

    # تاريخ آخر تحديث
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # العلاقات
    stock_movements = relationship("StockMovement", back_populates="warehouse")
    item_stocks = relationship("ItemStock", back_populates="warehouse")

    def __repr__(self):
        return f"<Warehouse(id={self.id}, number='{self.warehouse_number}', name='{self.name}')>"

class ItemStock(Base):
    """
    نموذج مخزون الصنف في المخزن
    Item Stock in Warehouse Model
    """
    __tablename__ = "item_stocks"

    # إضافة قيد فريد لضمان عدم تكرار الصنف في نفس المخزن
    __table_args__ = (
        UniqueConstraint('item_id', 'warehouse_id', name='uq_item_warehouse'),
    )

    # المعرف الفريد
    id = Column(Integer, primary_key=True, index=True)

    # معرف الصنف
    item_id = Column(Integer, ForeignKey('items.id'), nullable=False, index=True)

    # معرف المخزن
    warehouse_id = Column(Integer, ForeignKey('warehouses.id'), nullable=False, index=True)

    # الكمية المتاحة
    available_quantity = Column(Numeric(15, 3), default=0.000)

    # الكمية المحجوزة
    reserved_quantity = Column(Numeric(15, 3), default=0.000)

    # متوسط سعر التكلفة
    average_cost = Column(Numeric(15, 2), default=0.00)

    # تاريخ الإنشاء
    created_at = Column(DateTime(timezone=True), server_default=func.now())

    # تاريخ آخر تحديث
    last_updated = Column(DateTime(timezone=True), onupdate=func.now())

    # العلاقات
    item = relationship("Item", back_populates="item_stocks")
    warehouse = relationship("Warehouse", back_populates="item_stocks")

    def __repr__(self):
        return f"<ItemStock(item_id={self.item_id}, warehouse_id={self.warehouse_id}, quantity={self.available_quantity})>"

    @property
    def total_quantity(self):
        """إجمالي الكمية (متاحة + محجوزة)"""
        return self.available_quantity + self.reserved_quantity

class StockMovement(Base):
    """
    نموذج حركة المخزون
    Stock Movement Model
    """
    __tablename__ = "stock_movements"

    # المعرف الفريد
    id = Column(Integer, primary_key=True, index=True)

    # رقم الحركة
    movement_number = Column(String(20), unique=True, nullable=False, index=True)

    # تاريخ الحركة
    movement_date = Column(Date, nullable=False, index=True)

    # معرف الصنف
    item_id = Column(Integer, ForeignKey('items.id'), nullable=False)

    # معرف المخزن
    warehouse_id = Column(Integer, ForeignKey('warehouses.id'), nullable=False)

    # نوع الحركة (in, out, transfer, adjustment)
    movement_type = Column(String(20), nullable=False)

    # الكمية
    quantity = Column(Numeric(15, 3), nullable=False)

    # سعر الوحدة
    unit_cost = Column(Numeric(15, 2), default=0.00)

    # إجمالي التكلفة
    total_cost = Column(Numeric(15, 2), default=0.00)

    # المرجع (رقم الفاتورة، أمر التحويل، إلخ)
    reference = Column(String(50), nullable=True)

    # نوع المرجع (sales_invoice, purchase_invoice, transfer, adjustment)
    reference_type = Column(String(30), nullable=True)

    # معرف المرجع
    reference_id = Column(Integer, nullable=True)

    # وصف الحركة
    description = Column(Text, nullable=True)

    # المستخدم الذي أنشأ الحركة
    created_by = Column(Integer, ForeignKey('users.id'), nullable=False)

    # تاريخ الإنشاء
    created_at = Column(DateTime(timezone=True), server_default=func.now())

    # العلاقات
    item = relationship("Item", back_populates="stock_movements")
    warehouse = relationship("Warehouse", back_populates="stock_movements")
    creator = relationship("User")

    def __repr__(self):
        return f"<StockMovement(id={self.id}, number='{self.movement_number}', type='{self.movement_type}', quantity={self.quantity})>"
