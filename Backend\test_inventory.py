"""
اختبار بسيط لنظام إدارة المخزون
Simple test for inventory management system
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sqlalchemy.orm import Session
from database.config import get_db, engine, Base
from services.inventory_service import InventoryService
from core.schemas import (
    ItemCategoryCreate, UnitCreate, ItemCreate, WarehouseCreate
)

def test_inventory_system():
    """اختبار النظام"""
    print("🧪 بدء اختبار نظام إدارة المخزون...")
    
    # إنشاء الجداول
    Base.metadata.create_all(bind=engine)
    
    # الحصول على جلسة قاعدة البيانات
    db: Session = next(get_db())
    
    try:
        # إنشاء خدمة المخزون
        inventory_service = InventoryService(db)
        
        print("✅ تم إنشاء خدمة المخزون بنجاح")
        
        # اختبار إنشاء تصنيف
        category_data = ItemCategoryCreate(
            name="Electronics",
            name_ar="إلكترونيات",
            code="ELEC001",
            description="Electronic items and devices"
        )
        
        category = inventory_service.create_item_category(category_data)
        print(f"✅ تم إنشاء التصنيف: {category.name} - {category.name_ar}")
        
        # اختبار إنشاء وحدة قياس
        unit_data = UnitCreate(
            name="Piece",
            name_ar="قطعة",
            symbol="PCS",
            symbol_ar="قطعة",
            is_base_unit=True
        )
        
        unit = inventory_service.create_unit(unit_data)
        print(f"✅ تم إنشاء وحدة القياس: {unit.name} - {unit.name_ar}")
        
        # اختبار إنشاء مخزن
        warehouse_data = WarehouseCreate(
            warehouse_number="WH001",
            name="Main Warehouse",
            name_ar="المخزن الرئيسي",
            address="123 Main Street",
            city="Riyadh",
            is_main=True
        )
        
        warehouse = inventory_service.create_warehouse(warehouse_data)
        print(f"✅ تم إنشاء المخزن: {warehouse.name} - {warehouse.name_ar}")
        
        # اختبار إنشاء صنف
        item_data = ItemCreate(
            item_number="ITEM001",
            name="Laptop Computer",
            name_ar="جهاز كمبيوتر محمول",
            barcode="1234567890123",
            category_id=category.id,
            unit_id=unit.id,
            item_type="product",
            purchase_price=2000.00,
            selling_price=2500.00,
            minimum_stock=5.0,
            maximum_stock=50.0,
            reorder_point=10.0,
            description="High-performance laptop computer"
        )
        
        item = inventory_service.create_item(item_data)
        print(f"✅ تم إنشاء الصنف: {item.item_number} - {item.name}")
        
        # اختبار تعديل المخزون
        stock = inventory_service.adjust_item_stock(
            item_id=item.id,
            warehouse_id=warehouse.id,
            quantity_change=20.0,
            unit_cost=2000.00,
            description="رصيد افتتاحي"
        )
        print(f"✅ تم تعديل المخزون: الكمية المتاحة = {stock.available_quantity}")
        
        # اختبار الحصول على الأصناف
        items = inventory_service.get_items()
        print(f"✅ تم العثور على {len(items)} صنف")
        
        # اختبار تقرير الأصناف منخفضة المخزون
        low_stock_items = inventory_service.get_low_stock_items()
        print(f"✅ عدد الأصناف منخفضة المخزون: {len(low_stock_items)}")
        
        # اختبار تقييم المخزون
        valuation = inventory_service.get_stock_valuation()
        print(f"✅ قيمة المخزون الإجمالية: {valuation['total_value']} ريال")
        
        print("🎉 تم اجتياز جميع الاختبارات بنجاح!")
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {str(e)}")
        import traceback
        traceback.print_exc()
    
    finally:
        db.close()

if __name__ == "__main__":
    test_inventory_system()
