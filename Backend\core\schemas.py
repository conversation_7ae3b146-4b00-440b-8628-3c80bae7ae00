"""
نماذج البيانات (Pydantic Schemas)
Data Models for API validation
"""

from pydantic import BaseModel, EmailStr
from typing import Optional, List
from datetime import datetime, date
from decimal import Decimal

# نماذج المصادقة
class UserLogin(BaseModel):
    username: str
    password: str

class Token(BaseModel):
    access_token: str
    token_type: str

class TokenData(BaseModel):
    username: str = None

# نماذج المستخدم
class UserBase(BaseModel):
    username: str
    email: EmailStr
    full_name: str
    phone: Optional[str] = None
    is_active: bool = True

class UserCreate(UserBase):
    password: str

class UserUpdate(BaseModel):
    email: Optional[EmailStr] = None
    full_name: Optional[str] = None
    phone: Optional[str] = None
    is_active: Optional[bool] = None

class UserResponse(UserBase):
    id: int
    is_admin: bool
    created_at: datetime
    last_login: Optional[datetime] = None

    class Config:
        orm_mode = True

# نماذج أنواع الحسابات
class AccountTypeBase(BaseModel):
    name: str
    name_ar: str
    code: str
    nature: str
    display_order: int = 0

class AccountTypeCreate(AccountTypeBase):
    pass

class AccountTypeResponse(AccountTypeBase):
    id: int
    is_active: bool
    created_at: datetime

    class Config:
        orm_mode = True

# نماذج الحسابات
class AccountBase(BaseModel):
    account_number: str
    name: str
    name_ar: str
    account_type_id: int
    parent_id: Optional[int] = None
    level: int = 1
    is_parent: bool = False
    is_postable: bool = True
    opening_balance: Decimal = Decimal('0.00')
    description: Optional[str] = None

class AccountCreate(AccountBase):
    pass

class AccountUpdate(BaseModel):
    name: Optional[str] = None
    name_ar: Optional[str] = None
    parent_id: Optional[int] = None
    is_postable: Optional[bool] = None
    opening_balance: Optional[Decimal] = None
    description: Optional[str] = None
    is_active: Optional[bool] = None

class AccountResponse(AccountBase):
    id: int
    current_balance: Decimal
    is_active: bool
    created_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        orm_mode = True

# نماذج العملاء
class CustomerBase(BaseModel):
    customer_number: str
    name: str
    name_ar: str
    customer_type: str = "individual"
    identification_number: Optional[str] = None
    tax_number: Optional[str] = None
    phone: Optional[str] = None
    phone2: Optional[str] = None
    email: Optional[EmailStr] = None
    address: Optional[str] = None
    city: Optional[str] = None
    region: Optional[str] = None
    postal_code: Optional[str] = None
    country: str = "Saudi Arabia"
    opening_balance: Decimal = Decimal('0.00')
    credit_limit: Decimal = Decimal('0.00')
    payment_terms: int = 30
    default_discount: Decimal = Decimal('0.00')
    notes: Optional[str] = None

class CustomerCreate(CustomerBase):
    pass

class CustomerUpdate(BaseModel):
    name: Optional[str] = None
    name_ar: Optional[str] = None
    customer_type: Optional[str] = None
    identification_number: Optional[str] = None
    tax_number: Optional[str] = None
    phone: Optional[str] = None
    phone2: Optional[str] = None
    email: Optional[EmailStr] = None
    address: Optional[str] = None
    city: Optional[str] = None
    region: Optional[str] = None
    postal_code: Optional[str] = None
    country: Optional[str] = None
    credit_limit: Optional[Decimal] = None
    payment_terms: Optional[int] = None
    default_discount: Optional[Decimal] = None
    notes: Optional[str] = None
    is_active: Optional[bool] = None

class CustomerResponse(CustomerBase):
    id: int
    current_balance: Decimal
    is_active: bool
    created_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        orm_mode = True

# نماذج الموردين
class SupplierBase(BaseModel):
    supplier_number: str
    name: str
    name_ar: str
    supplier_type: str = "company"
    identification_number: Optional[str] = None
    tax_number: Optional[str] = None
    phone: Optional[str] = None
    phone2: Optional[str] = None
    email: Optional[EmailStr] = None
    address: Optional[str] = None
    city: Optional[str] = None
    region: Optional[str] = None
    postal_code: Optional[str] = None
    country: str = "Saudi Arabia"
    opening_balance: Decimal = Decimal('0.00')
    credit_limit: Decimal = Decimal('0.00')
    payment_terms: int = 30
    default_discount: Decimal = Decimal('0.00')
    contact_person: Optional[str] = None
    contact_position: Optional[str] = None
    notes: Optional[str] = None

class SupplierCreate(SupplierBase):
    pass

class SupplierUpdate(BaseModel):
    name: Optional[str] = None
    name_ar: Optional[str] = None
    supplier_type: Optional[str] = None
    identification_number: Optional[str] = None
    tax_number: Optional[str] = None
    phone: Optional[str] = None
    phone2: Optional[str] = None
    email: Optional[EmailStr] = None
    address: Optional[str] = None
    city: Optional[str] = None
    region: Optional[str] = None
    postal_code: Optional[str] = None
    country: Optional[str] = None
    credit_limit: Optional[Decimal] = None
    payment_terms: Optional[int] = None
    default_discount: Optional[Decimal] = None
    contact_person: Optional[str] = None
    contact_position: Optional[str] = None
    notes: Optional[str] = None
    is_active: Optional[bool] = None

class SupplierResponse(SupplierBase):
    id: int
    current_balance: Decimal
    is_active: bool
    created_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        orm_mode = True

# ===== نماذج الأصناف والمخزون =====

# نماذج تصنيفات الأصناف
class ItemCategoryBase(BaseModel):
    name: str
    name_ar: str
    code: str
    parent_id: Optional[int] = None
    description: Optional[str] = None
    is_active: bool = True

class ItemCategoryCreate(ItemCategoryBase):
    pass

class ItemCategoryUpdate(BaseModel):
    name: Optional[str] = None
    name_ar: Optional[str] = None
    code: Optional[str] = None
    parent_id: Optional[int] = None
    description: Optional[str] = None
    is_active: Optional[bool] = None

class ItemCategoryResponse(ItemCategoryBase):
    id: int
    created_at: datetime
    children: List['ItemCategoryResponse'] = []

    class Config:
        orm_mode = True

# نماذج وحدات القياس
class UnitBase(BaseModel):
    name: str
    name_ar: str
    symbol: str
    symbol_ar: str
    is_base_unit: bool = False
    conversion_factor: Decimal = Decimal('1.0000')
    is_active: bool = True

class UnitCreate(UnitBase):
    pass

class UnitUpdate(BaseModel):
    name: Optional[str] = None
    name_ar: Optional[str] = None
    symbol: Optional[str] = None
    symbol_ar: Optional[str] = None
    is_base_unit: Optional[bool] = None
    conversion_factor: Optional[Decimal] = None
    is_active: Optional[bool] = None

class UnitResponse(UnitBase):
    id: int
    created_at: datetime

    class Config:
        orm_mode = True

# نماذج الأصناف
class ItemBase(BaseModel):
    item_number: str
    name: str
    name_ar: str
    barcode: Optional[str] = None
    category_id: Optional[int] = None
    unit_id: int
    item_type: str = "product"
    purchase_price: Decimal = Decimal('0.00')
    selling_price: Decimal = Decimal('0.00')
    minimum_stock: Decimal = Decimal('0.00')
    maximum_stock: Decimal = Decimal('0.00')
    reorder_point: Decimal = Decimal('0.00')
    description: Optional[str] = None
    notes: Optional[str] = None
    is_active: bool = True
    track_inventory: bool = True

class ItemCreate(ItemBase):
    pass

class ItemUpdate(BaseModel):
    name: Optional[str] = None
    name_ar: Optional[str] = None
    barcode: Optional[str] = None
    category_id: Optional[int] = None
    unit_id: Optional[int] = None
    item_type: Optional[str] = None
    purchase_price: Optional[Decimal] = None
    selling_price: Optional[Decimal] = None
    minimum_stock: Optional[Decimal] = None
    maximum_stock: Optional[Decimal] = None
    reorder_point: Optional[Decimal] = None
    description: Optional[str] = None
    notes: Optional[str] = None
    is_active: Optional[bool] = None
    track_inventory: Optional[bool] = None

class ItemResponse(ItemBase):
    id: int
    created_at: datetime
    updated_at: Optional[datetime] = None
    category: Optional[ItemCategoryResponse] = None
    unit: UnitResponse
    total_stock: Decimal = Decimal('0.00')
    total_reserved: Decimal = Decimal('0.00')
    is_low_stock: bool = False
    profit_margin: Decimal = Decimal('0.00')

    class Config:
        orm_mode = True

# نماذج المخازن
class WarehouseBase(BaseModel):
    warehouse_number: str
    name: str
    name_ar: str
    address: Optional[str] = None
    city: Optional[str] = None
    region: Optional[str] = None
    phone: Optional[str] = None
    manager_name: Optional[str] = None
    is_main: bool = False
    is_active: bool = True
    notes: Optional[str] = None

class WarehouseCreate(WarehouseBase):
    pass

class WarehouseUpdate(BaseModel):
    name: Optional[str] = None
    name_ar: Optional[str] = None
    address: Optional[str] = None
    city: Optional[str] = None
    region: Optional[str] = None
    phone: Optional[str] = None
    manager_name: Optional[str] = None
    is_main: Optional[bool] = None
    is_active: Optional[bool] = None
    notes: Optional[str] = None

class WarehouseResponse(WarehouseBase):
    id: int
    created_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        orm_mode = True

# نماذج مخزون الأصناف
class ItemStockBase(BaseModel):
    item_id: int
    warehouse_id: int
    available_quantity: Decimal = Decimal('0.000')
    reserved_quantity: Decimal = Decimal('0.000')
    average_cost: Decimal = Decimal('0.00')

class ItemStockCreate(ItemStockBase):
    pass

class ItemStockUpdate(BaseModel):
    available_quantity: Optional[Decimal] = None
    reserved_quantity: Optional[Decimal] = None
    average_cost: Optional[Decimal] = None

class ItemStockResponse(ItemStockBase):
    id: int
    created_at: Optional[datetime] = None
    last_updated: Optional[datetime] = None
    item: ItemResponse
    warehouse: WarehouseResponse
    total_quantity: Decimal = Decimal('0.000')

    class Config:
        orm_mode = True

# نماذج حركات المخزون
class StockMovementBase(BaseModel):
    movement_date: date
    item_id: int
    warehouse_id: int
    movement_type: str  # in, out, transfer, adjustment
    quantity: Decimal
    unit_cost: Decimal = Decimal('0.00')
    total_cost: Decimal = Decimal('0.00')
    reference: Optional[str] = None
    reference_type: Optional[str] = None
    reference_id: Optional[int] = None
    description: Optional[str] = None

class StockMovementCreate(StockMovementBase):
    pass

class StockMovementResponse(StockMovementBase):
    id: int
    movement_number: str
    created_by: int
    created_at: datetime
    item: ItemResponse
    warehouse: WarehouseResponse

    class Config:
        orm_mode = True

# نماذج الاستجابة العامة
class MessageResponse(BaseModel):
    message: str
    message_ar: Optional[str] = None
    success: bool = True

class PaginatedResponse(BaseModel):
    items: List
    total: int
    page: int
    size: int
    pages: int

# تحديث النماذج للمراجع الأمامية (Pydantic v1)
ItemCategoryResponse.update_forward_refs()
