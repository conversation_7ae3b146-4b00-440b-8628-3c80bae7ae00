"""
التطبيق الرئيسي للمحاسبة المالية
Main Accounting Application
"""

import uvicorn
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware

# إنشاء تطبيق FastAPI
app = FastAPI(
    title="نظام المحاسبة المالية المتكامل",
    description="Integrated Financial Accounting System",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# إعداد CORS للسماح للواجهة الأمامية بالوصول
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # في الإنتاج، يجب تحديد النطاقات المسموحة
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.on_event("startup")
async def startup_event():
    """
    أحداث بدء التشغيل
    Startup events
    """
    print("🚀 بدء تشغيل نظام المحاسبة المالية...")
    print("🚀 Starting Financial Accounting System...")
    print("✅ النظام جاهز للعمل")
    print("✅ System ready")

@app.on_event("shutdown")
async def shutdown_event():
    """
    أحداث إيقاف التشغيل
    Shutdown events
    """
    print("🛑 إيقاف تشغيل نظام المحاسبة المالية...")
    print("🛑 Shutting down Financial Accounting System...")

@app.get("/")
async def root():
    """
    الصفحة الرئيسية
    Root endpoint
    """
    return {
        "message": "مرحباً بك في نظام المحاسبة المالية المتكامل",
        "message_en": "Welcome to Integrated Financial Accounting System",
        "version": "1.0.0",
        "status": "running"
    }

@app.get("/health")
async def health_check():
    """
    فحص حالة النظام
    Health check endpoint
    """
    return {
        "status": "healthy",
        "message": "النظام يعمل بشكل طبيعي",
        "message_en": "System is running normally"
    }

@app.get("/api/info")
async def api_info():
    """
    معلومات API
    API information
    """
    return {
        "api_name": "نظام المحاسبة المالية",
        "api_name_en": "Financial Accounting System",
        "version": "1.0.0",
        "modules": [
            "المحاسبة العامة - General Accounting",
            "المبيعات والعملاء - Sales & Customers",
            "المشتريات والموردين - Purchases & Suppliers",
            "المخازن والأصناف - Inventory & Items",
            "الصندوق والبنوك - Cash & Banks",
            "التقارير المالية - Financial Reports"
        ]
    }

# استيراد وتسجيل المسارات
from api.auth import router as auth_router
from api.accounting import router as accounting_router
from api.inventory import router as inventory_router

app.include_router(auth_router, prefix="/api/auth", tags=["authentication"])
app.include_router(accounting_router, prefix="/api/accounting", tags=["accounting"])
app.include_router(inventory_router, prefix="/api/inventory", tags=["inventory"])

if __name__ == "__main__":
    print("🔧 تشغيل الخادم في وضع التطوير...")
    print("🔧 Running server in development mode...")
    uvicorn.run(
        "main:app",
        host="127.0.0.1",
        port=8000,
        reload=True,
        log_level="info"
    )
