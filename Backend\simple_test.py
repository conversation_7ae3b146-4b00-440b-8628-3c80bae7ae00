"""
اختبار بسيط للتأكد من عمل النماذج
Simple test to verify models work
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """اختبار الاستيراد"""
    try:
        print("🧪 اختبار استيراد النماذج...")
        
        # اختبار استيراد النماذج
        from models.inventory.item import ItemCategory, Unit, Item
        print("✅ تم استيراد نماذج الأصناف بنجاح")
        
        from models.inventory.warehouse import Warehouse, ItemStock, StockMovement
        print("✅ تم استيراد نماذج المخازن بنجاح")
        
        # اختبار إنشاء كائنات بسيطة
        category = ItemCategory(
            name="Test Category",
            name_ar="تصنيف تجريبي",
            code="TEST001"
        )
        print(f"✅ تم إنشاء تصنيف: {category}")
        
        unit = Unit(
            name="Piece",
            name_ar="قطعة",
            symbol="PCS",
            symbol_ar="قطعة"
        )
        print(f"✅ تم إنشاء وحدة: {unit}")
        
        print("🎉 جميع الاختبارات نجحت!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_imports()
